import os
from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.rerankers import <PERSON>wenR<PERSON>ker
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

# ================== 1. 本地模型路径配置 ==================
# 替换为本地 Qwen3-8B 的路径
QWEN3_MODEL_PATH = "./Qwen3-8B-Base"  # 确保已下载 Qwen3-8B 模型
QWEN3_EMBEDDING_PATH = "Qwen/Qwen3-Embedding-4096"  # 支持中文长文本
QWEN3_RERANKER_PATH = "Qwen/Qwen3-Reranker-8B"  # 可选：重排序模型

# ================== 2. 文档预处理 ==================
def load_and_split_documents(pdf_path):
    loader = PyPDFLoader(pdf_path)
    documents = loader.load()
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,  # 按条款级切分
        chunk_overlap=50
    )
    split_docs = text_splitter.split_documents(documents)
    return split_docs

# ================== 3. 向量化与构建 FAISS 数据库 ==================
def build_vectorstore(docs, embedding_model_path):
    embedding_model = HuggingFaceEmbeddings(model_name=embedding_model_path)
    vectorstore = FAISS.from_documents(docs, embedding_model)
    vectorstore.save_local("policy_vectorstore")  # 保存向量数据库
    return vectorstore

# ================== 4. 加载本地 Qwen3-8B 模型 ==================
def load_qwen3_model(model_path):
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="auto",  # 自动分配显存
        trust_remote_code=True,
        torch_dtype="auto"  # 自动选择精度（FP16/INT8）
    ).eval()
    return tokenizer, model

# ================== 5. 定义提示词模板 ==================
def create_prompt_template():
    prompt_template = """
    你是一个政策合规专家，请严格依据以下检索到的政策条款回答问题：
    {context}

    问题：{question}

    回答要求：
    1. 必须引用具体政策名称和条款编号（如《XX办法》第X条）
    2. 使用集团公文格式（总则→细则→附则）
    """
    return PromptTemplate.from_template(prompt_template)

# ================== 6. 构建 RAG 链 ==================
def build_rag_chain(vectorstore, tokenizer, model, prompt_template):
    # 加载本地模型作为 LLM
    llm_pipeline = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        max_new_tokens=200,
        temperature=0.3
    )
    
    # 构建检索器
    retriever = vectorstore.as_retriever(search_kwargs={"k": 5})  # 返回Top-5结果
    
    # 构建 RAG 链
    chain = RetrievalQA.from_chain_type(
        llm=llm_pipeline,
        chain_type="stuff",
        retriever=retriever,
        chain_type_kwargs={"prompt": prompt_template}
    )
    return chain

# ================== 7. 主函数 ==================
def main():
    # 1. 加载并切分文档
    print("1. 加载并切分文档...")
    pdf_path = "./files/1.1.1云南省推动高速公路融合发展若干政策措施（试行）.pdf"  # 替换为实际政策文件路径
    split_docs = load_and_split_documents(pdf_path)

    # 2. 构建向量数据库
    print("2. 构建向量数据库...")
    vectorstore = build_vectorstore(split_docs, QWEN3_EMBEDDING_PATH)

    # 3. 加载本地 Qwen3-8B 模型
    print("3. 加载本地 Qwen3-8B 模型...")
    tokenizer, model = load_qwen3_model(QWEN3_MODEL_PATH)

    # 4. 定义提示词模板
    print("4. 定义提示词模板...")
    prompt_template = create_prompt_template()

    # 5. 构建 RAG 链
    print("5. 构建 RAG 链...")
    rag_chain = build_rag_chain(vectorstore, tokenizer, model, prompt_template)

    # 6. 测试查询
    print("6. 测试查询...")
    query = "如何制定数据安全应急预案？"
    response = rag_chain.run(query)
    print(f"问题: {query}")
    print(f"回答: {response}")

if __name__ == "__main__":
    main()